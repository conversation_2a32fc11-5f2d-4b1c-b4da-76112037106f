using AIChara;
using BepInEx;
using BepInEx.Logging;
using Studio;
using UnityEngine;
using System;
using UniRx;
using System.Collections.Generic;

[BepInPlugin("com.yourname.characterinfoui", "CharacterInfoUI", "1.0.0")]
public class CharacterInfoUI : BaseUnityPlugin
{
    private bool _showUI = false;
    private Vector2 _scrollPosition = Vector2.zero;
    private OCIChar _selectedCharacter = null;
    private int _selectedObjectId = -1;
    
    // Cache frequently used values
    private const float PiOver180 = Mathf.PI / 180f;
    private float _angle = 0f;
    private float _lastUpdateTime = 0f;
    private const float UpdateInterval = 1.0f; // Update physics at 10Hz instead of every frame
    
    // Cache the clothes IDs to avoid recreating the list
    private static readonly int[] ClothesIds = {1, 2, 3, 4, 5, 6, 7};
    private byte _clothesState = 0;
    private const int ClothesStateChangeInterval = 5; // frames
    
    private List<OCIChar> _cachedCharacters = new();
    private List<CharacterInfo> _characterList = new();
	private struct CharacterInfo
	{
		public int id;
		public string name;
		public Vector3 position;
	}
    
    private void Awake()
    {
        Logger.LogInfo("CharacterInfoUI plugin loaded");
    }

    private void Update()
    {
        // Only check for input every frame
        if (Input.GetKeyDown(KeyCode.F3))
        {
            _showUI = !_showUI;
        }
        if (Time.frameCount % 30 == 0)
        {
			CacheCharacters();
        }
        if (_cachedCharacters.Count > 0)
        {
			_selectedCharacter = _cachedCharacters[0];
		}
        if (_selectedCharacter == null) return;
        
        // Throttle physics updates
        _lastUpdateTime += Time.deltaTime;
        if (_lastUpdateTime < UpdateInterval) return;
        
        _lastUpdateTime = 0f;
        
        // Update character position
        _angle += UpdateInterval * 3.1415926f;
        if (_angle > 360f) _angle -= 360f;
        
        float posX = Mathf.Sin(_angle);
        float posZ = Mathf.Cos(_angle);
        Vector3 pos = new Vector3(posX, 0.5f, posZ);
        _selectedCharacter.oiCharInfo.changeAmount.pos = pos;
        
        // Update clothes state less frequently
        if (Time.frameCount % ClothesStateChangeInterval == 0)
        {
            _clothesState = (byte)((_clothesState + 1) % 3);
            foreach (var id in ClothesIds)
            {
                _selectedCharacter.SetClothesState(id, _clothesState);
            }
        }
    }

    private void OnGUI()
    {
        if (!_showUI) return;

        // Only create the window when needed
        GUI.Window(0, new Rect(10, 10, 300, 520), DrawUI, "Character Info");
    }

    private void DrawUI(int windowID)
    {
        _scrollPosition = GUILayout.BeginScrollView(_scrollPosition, GUILayout.Width(280), GUILayout.Height(500));
        
        foreach (var kvp in _cachedCharacters)
        {
			GUILayout.Label($"Char: {kvp}");
        }
        GUILayout.EndScrollView();
        
    }
    
    private void CacheCharacters()
    {
        if (Studio.Studio.Instance == null) return;
        _cachedCharacters.Clear();
        foreach (var obj in Studio.Studio.Instance.dicObjectCtrl.Values)
        {
			if (obj is OCIChar ociChar)
			{
				_cachedCharacters.Add(ociChar);
			}
        }
        
    }
}