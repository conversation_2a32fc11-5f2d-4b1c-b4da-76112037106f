using BepInEx;
using BepInEx.Logging;
using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using Mono.Cecil;

namespace MyFirstPlugin
{
    [BepInPlugin("com.yourname.characterinfoui", "CharacterInfoUI", "1.0.0")]
    public class Plugin : BaseUnityPlugin
    {
        private bool showUI = false;
        private UIDesigner uiDesigner;
        private bool response = false;
        public static Vector3 mousePos;
        public static GameObject currentCharacter = null;

        private void Awake()
        {
            // 初始化UIDesigner实例
            uiDesigner = new UIDesigner(this);
        }

        private void Update()
        {
            if (Input.GetKeyDown(KeyCode.F3))
            {
                showUI = !showUI;
            }

            // 自动获取角色
            AutoSelectCharacter();

            mousePos = Input.mousePosition;

            // 更新移动控制
            uiDesigner.UpdateMovement();
        }

        private void AutoSelectCharacter()
        {
            if (currentCharacter != null) return; // 如果已经有角色就不重复查找

            // 方法1：尝试通过您原来的方式查找
            GameObject character = GameObject.Find("CommonSpace/chaF_001");
            if (character != null)
            {
                currentCharacter = character;
                Debug.Log("通过CommonSpace路径找到角色: " + character.name);
                return;
            }

            // 方法2：尝试查找其他常见的角色路径
            string[] possiblePaths = {
                "CommonSpace/chaF_001",
                "CommonSpace/chaM_001",
                "chaF_001",
                "chaM_001"
            };

            foreach (string path in possiblePaths)
            {
                character = GameObject.Find(path);
                if (character != null)
                {
                    currentCharacter = character;
                    Debug.Log("找到角色: " + character.name + " 路径: " + path);
                    return;
                }
            }

            // 方法3：通过Studio API查找（如果可用）
            TryFindCharacterViaStudio();
        }

        private void TryFindCharacterViaStudio()
        {
            try
            {
                var studioType = System.Type.GetType("Studio.Studio, Assembly-CSharp");
                if (studioType != null)
                {
                    var instanceProperty = studioType.GetProperty("Instance");
                    if (instanceProperty != null)
                    {
                        var studioInstance = instanceProperty.GetValue(null);
                        if (studioInstance != null)
                        {
                            var dicObjectCtrlField = studioInstance.GetType().GetField("dicObjectCtrl");
                            if (dicObjectCtrlField != null)
                            {
                                var dicObjectCtrl = dicObjectCtrlField.GetValue(studioInstance);
                                if (dicObjectCtrl != null)
                                {
                                    var dict = dicObjectCtrl as System.Collections.IDictionary;
                                    if (dict != null)
                                    {
                                        foreach (var kvp in dict)
                                        {
                                            var objectCtrl = ((System.Collections.DictionaryEntry)kvp).Value;
                                            if (objectCtrl.GetType().Name.Contains("OCIChar"))
                                            {
                                                // 尝试获取角色的GameObject
                                                var charInfoField = objectCtrl.GetType().GetField("charInfo");
                                                if (charInfoField != null)
                                                {
                                                    var charInfo = charInfoField.GetValue(objectCtrl);
                                                    if (charInfo != null)
                                                    {
                                                        var gameObjectField = charInfo.GetType().GetField("gameObject");
                                                        if (gameObjectField != null)
                                                        {
                                                            var gameObject = gameObjectField.GetValue(charInfo) as GameObject;
                                                            if (gameObject != null)
                                                            {
                                                                currentCharacter = gameObject;
                                                                Debug.Log("通过Studio API找到角色: " + gameObject.name);
                                                                return;
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError("通过Studio API查找角色失败: " + e.Message);
            }
        }

        private void OnGUI()
        {
            if (showUI)
            {
                GUI.Window(0, new Rect(10, 10, 350, 700), uiDesigner.DrawWindow, "Character Control UI");
            }
        }
    }

    public class UIDesigner
    {
        private Plugin plugin;

        // 移动控制相关变量
        private bool isLinearMoving = false;
        private bool isCircularMoving = false;
        private bool isSquareMoving = false;
        private bool isFirstPersonMode = false;
        private float moveSpeed = 2.0f;
        private float circleRadius = 3.0f;
        private float squareSize = 4.0f;
        private float firstPersonMoveSpeed = 3.0f;

        // 移动状态变量
        private Vector3 startPosition;
        private Vector3 targetPosition;
        private float moveTimer = 0f;
        private int squareMoveStep = 0;

        public UIDesigner(Plugin plugin)
        {
            this.plugin = plugin;
        }

        public void DrawWindow(int windowID)
        {
            GUILayout.BeginVertical();

            // 角色状态显示
            GUILayout.Label("=== 角色状态 ===");
            if (Plugin.currentCharacter != null)
            {
                GUILayout.Label("角色: " + Plugin.currentCharacter.name);
                GUILayout.Label("位置: " + Plugin.currentCharacter.transform.position.ToString("F2"));
            }
            else
            {
                GUILayout.Label("正在搜索角色...");
                if (GUILayout.Button("手动刷新角色"))
                {
                    Plugin.currentCharacter = null; // 重置以触发重新搜索
                }
            }

            GUILayout.Space(10);

            // 移动控制区域
            GUILayout.Label("=== 移动控制 ===");

            if (Plugin.currentCharacter != null)
            {
                // 移动速度控制
                GUILayout.Label($"移动速度: {moveSpeed:F1}");
                moveSpeed = GUILayout.HorizontalSlider(moveSpeed, 0.5f, 5.0f);

                GUILayout.Space(5);

                // 直线移动
                if (GUILayout.Button(isLinearMoving ? "停止直线移动" : "开始直线移动"))
                {
                    ToggleLinearMovement();
                }

                // 圆形移动
                GUILayout.Label($"圆形半径: {circleRadius:F1}");
                circleRadius = GUILayout.HorizontalSlider(circleRadius, 1.0f, 5.0f);

                if (GUILayout.Button(isCircularMoving ? "停止圆形移动" : "开始圆形移动"))
                {
                    ToggleCircularMovement();
                }

                // 方形移动
                GUILayout.Label($"方形大小: {squareSize:F1}");
                squareSize = GUILayout.HorizontalSlider(squareSize, 2.0f, 6.0f);

                if (GUILayout.Button(isSquareMoving ? "停止方形移动" : "开始方形移动"))
                {
                    ToggleSquareMovement();
                }

                GUILayout.Space(10);

                // 第一人称模式
                GUILayout.Label("=== 第一人称模式 ===");

                if (GUILayout.Button(isFirstPersonMode ? "退出第一人称" : "进入第一人称"))
                {
                    ToggleFirstPersonMode();
                }

                if (isFirstPersonMode)
                {
                    GUILayout.Label($"移动速度: {firstPersonMoveSpeed:F1}");
                    firstPersonMoveSpeed = GUILayout.HorizontalSlider(firstPersonMoveSpeed, 1.0f, 10.0f);
                    GUILayout.Label("使用WASD键移动");
                    GUILayout.Label("W-前进, S-后退, A-左移, D-右移");
                }

                GUILayout.Space(10);

                // 服装控制区域
                GUILayout.Label("=== 服装控制 ===");

                if (GUILayout.Button("全部穿上"))
                {
                    SetClothesState(0); // 穿
                }

                if (GUILayout.Button("全部半脱"))
                {
                    SetClothesState(1); // 半脱
                }

                if (GUILayout.Button("全部脱掉"))
                {
                    SetClothesState(2); // 脱
                }

                GUILayout.Space(10);

                // 状态显示
                GUILayout.Label("=== 状态信息 ===");
                GUILayout.Label("移动状态: " + GetMovementStatus());
            }
            else
            {
                GUILayout.Label("请等待角色加载...");
            }

            GUILayout.Label("鼠标位置: " + Plugin.mousePos.ToString("F0"));

            GUILayout.EndVertical();
            GUI.DragWindow();
        }

        // 更新移动控制
        public void UpdateMovement()
        {
            if (Plugin.currentCharacter == null) return;

            if (isFirstPersonMode)
            {
                UpdateFirstPersonMovement();
            }
            else if (isLinearMoving)
            {
                UpdateLinearMovement();
            }
            else if (isCircularMoving)
            {
                UpdateCircularMovement();
            }
            else if (isSquareMoving)
            {
                UpdateSquareMovement();
            }
        }

        // 切换直线移动
        private void ToggleLinearMovement()
        {
            if (Plugin.currentCharacter == null) return;

            isLinearMoving = !isLinearMoving;
            if (isLinearMoving)
            {
                // 停止其他移动
                isCircularMoving = false;
                isSquareMoving = false;
                isFirstPersonMode = false;

                startPosition = Plugin.currentCharacter.transform.position;
                targetPosition = startPosition + Vector3.forward * 5f;
                moveTimer = 0f;
            }
        }

        // 切换圆形移动
        private void ToggleCircularMovement()
        {
            if (Plugin.currentCharacter == null) return;

            isCircularMoving = !isCircularMoving;
            if (isCircularMoving)
            {
                // 停止其他移动
                isLinearMoving = false;
                isSquareMoving = false;
                isFirstPersonMode = false;

                startPosition = Plugin.currentCharacter.transform.position;
                moveTimer = 0f;
            }
        }

        // 切换方形移动
        private void ToggleSquareMovement()
        {
            if (Plugin.currentCharacter == null) return;

            isSquareMoving = !isSquareMoving;
            if (isSquareMoving)
            {
                // 停止其他移动
                isLinearMoving = false;
                isCircularMoving = false;
                isFirstPersonMode = false;

                startPosition = Plugin.currentCharacter.transform.position;
                squareMoveStep = 0;
                moveTimer = 0f;
            }
        }

        // 切换第一人称模式
        private void ToggleFirstPersonMode()
        {
            if (Plugin.currentCharacter == null) return;

            isFirstPersonMode = !isFirstPersonMode;
            if (isFirstPersonMode)
            {
                // 停止其他移动
                isLinearMoving = false;
                isCircularMoving = false;
                isSquareMoving = false;

                Debug.Log("进入第一人称模式");
            }
            else
            {
                Debug.Log("退出第一人称模式");
            }
        }

        // 更新直线移动
        private void UpdateLinearMovement()
        {
            moveTimer += Time.deltaTime * moveSpeed;
            float t = Mathf.PingPong(moveTimer, 1f);
            Vector3 newPosition = Vector3.Lerp(startPosition, targetPosition, t);
            Plugin.currentCharacter.transform.position = newPosition;
        }

        // 更新圆形移动
        private void UpdateCircularMovement()
        {
            moveTimer += Time.deltaTime * moveSpeed;
            float angle = moveTimer;
            Vector3 offset = new Vector3(
                Mathf.Cos(angle) * circleRadius,
                0,
                Mathf.Sin(angle) * circleRadius
            );
            Plugin.currentCharacter.transform.position = startPosition + offset;
        }

        // 更新方形移动
        private void UpdateSquareMovement()
        {
            moveTimer += Time.deltaTime * moveSpeed;

            if (moveTimer >= 1f)
            {
                moveTimer = 0f;
                squareMoveStep = (squareMoveStep + 1) % 4;
            }

            Vector3 offset = Vector3.zero;
            float t = moveTimer;

            switch (squareMoveStep)
            {
                case 0: // 向右
                    offset = Vector3.Lerp(Vector3.zero, Vector3.right * squareSize, t);
                    break;
                case 1: // 向前
                    offset = Vector3.right * squareSize + Vector3.Lerp(Vector3.zero, Vector3.forward * squareSize, t);
                    break;
                case 2: // 向左
                    offset = Vector3.right * squareSize + Vector3.forward * squareSize + Vector3.Lerp(Vector3.zero, Vector3.left * squareSize, t);
                    break;
                case 3: // 向后
                    offset = Vector3.forward * squareSize + Vector3.Lerp(Vector3.zero, Vector3.back * squareSize, t);
                    break;
            }

            Plugin.currentCharacter.transform.position = startPosition + offset;
        }

        // 更新第一人称移动
        private void UpdateFirstPersonMovement()
        {
            Vector3 currentPos = Plugin.currentCharacter.transform.position;
            Quaternion currentRot = Plugin.currentCharacter.transform.rotation;
            Vector3 moveDirection = Vector3.zero;

            // WASD控制
            if (Input.GetKey(KeyCode.W)) // 前进
            {
                moveDirection += currentRot * Vector3.forward;
            }
            if (Input.GetKey(KeyCode.S)) // 后退
            {
                moveDirection += currentRot * Vector3.back;
            }
            if (Input.GetKey(KeyCode.A)) // 左移
            {
                moveDirection += currentRot * Vector3.left;
            }
            if (Input.GetKey(KeyCode.D)) // 右移
            {
                moveDirection += currentRot * Vector3.right;
            }

            // 应用移动
            if (moveDirection != Vector3.zero)
            {
                moveDirection.Normalize();
                Vector3 newPosition = currentPos + moveDirection * firstPersonMoveSpeed * Time.deltaTime;
                Plugin.currentCharacter.transform.position = newPosition;
            }
        }

        // 设置服装状态
        private void SetClothesState(int state)
        {
            if (Plugin.currentCharacter == null) return;

            try
            {
                // 尝试通过反射调用SetClothesState方法
                var component = Plugin.currentCharacter.GetComponent("ChaControl");
                if (component != null)
                {
                    var method = component.GetType().GetMethod("SetClothesState");
                    if (method != null)
                    {
                        // 设置所有服装类型 (0-7)
                        for (int i = 0; i <= 7; i++)
                        {
                            method.Invoke(component, new object[] { i, state });
                        }
                        Debug.Log($"设置所有服装状态为: {state}");
                        return;
                    }
                }

                // 如果上面的方法不行，尝试其他可能的组件名
                string[] possibleComponents = { "ChaControl", "CharaControl", "Character" };
                foreach (string componentName in possibleComponents)
                {
                    component = Plugin.currentCharacter.GetComponent(componentName);
                    if (component != null)
                    {
                        var method = component.GetType().GetMethod("SetClothesState");
                        if (method != null)
                        {
                            for (int i = 0; i <= 7; i++)
                            {
                                method.Invoke(component, new object[] { i, state });
                            }
                            Debug.Log($"通过{componentName}设置服装状态为: {state}");
                            return;
                        }
                    }
                }

                Debug.LogWarning("未找到SetClothesState方法");
            }
            catch (System.Exception e)
            {
                Debug.LogError("设置服装状态失败: " + e.Message);
            }
        }

        // 获取移动状态
        private string GetMovementStatus()
        {
            if (isFirstPersonMode) return "第一人称模式";
            if (isLinearMoving) return "直线移动中";
            if (isCircularMoving) return "圆形移动中";
            if (isSquareMoving) return "方形移动中";
            return "静止";
        }
    }
}