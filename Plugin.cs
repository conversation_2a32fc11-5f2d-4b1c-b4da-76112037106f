using BepInEx;
using BepInEx.Logging;
using Studio;
using UnityEngine;
using System;

[BepInPlugin("com.yourname.charactercontrol", "Character Control Plugin", "1.0.0")]
public class CharacterControlPlugin : BaseUnityPlugin
{
    private ManualLogSource Logger;
    
    // Character control
    private OCIChar targetChar;
    private FirstPersonController fpvInstance;
    private IKAttacher ikAttacherInstance;
    private GameObject objectToFollowIK;

    void Awake()
    {
        Logger = base.Logger;
        Logger.LogInfo("Character Control Plugin loaded!");
    }

    void Start()
    {
        // Create a red cube for IK demonstration
        objectToFollowIK = GameObject.CreatePrimitive(PrimitiveType.Cube);
        objectToFollowIK.transform.localScale = new Vector3(0.1f, 0.1f, 0.1f);
        objectToFollowIK.GetComponent<Renderer>().material.color = Color.red;
        objectToFollowIK.GetComponent<Collider>().enabled = false;
        objectToFollowIK.SetActive(false);
    }

    void Update()
    {
        // F1: Select target character
        if (Input.GetKeyDown(KeyCode.F1))
        {
            FindTargetCharacter();
        }

        if (targetChar == null) return;

        // F2: Move and rotate
        if (Input.GetKeyDown(KeyCode.F2))
        {
            DemonstrateMovement();
        }

        // F3: Toggle animation
        if (Input.GetKeyDown(KeyCode.F3))
        {
            DemonstrateAnimation();
        }

        // F4: Toggle clothing (top)
        if (Input.GetKeyDown(KeyCode.F4))
        {
            DemonstrateClothing();
        }

        // F5: Toggle first-person view
        if (Input.GetKeyDown(KeyCode.F5))
        {
            ToggleFirstPersonView();
        }

        // F6: Ray interaction (teleport)
        if (Input.GetKeyDown(KeyCode.F6))
        {
            DemonstrateInteraction();
        }

        // F7: Toggle IK attachment
        if (Input.GetKeyDown(KeyCode.F7))
        {
            ToggleIKAttachment();
        }
    }

    void FindTargetCharacter()
    {
        // Get the first selected object
        if (Studio.Studio.Instance.treeNodeCtrl.selectObjectCtrl.Length == 0) 
        {
            Logger.LogInfo("No object selected");
            targetChar = null;
            return;
        }
        
        ObjectCtrlInfo selectedObject = Studio.Studio.Instance.treeNodeCtrl.selectObjectCtrl[0];
        targetChar = selectedObject as OCIChar;
        if (targetChar != null)
        {
            Logger.LogInfo("Locked character: " + targetChar.charInfo.name);
        }
        else
        {
            Logger.LogInfo("Selected object is not a character");
        }
    }

    void DemonstrateMovement()
    {
        if (targetChar == null) return;
        
        Logger.LogInfo("Moving character: Up 0.5m, rotate 45° right");
        Transform charTransform = targetChar.guideObject.transform;
        charTransform.Translate(0, 0.5f, 0, Space.World);
        charTransform.Rotate(0, 45f, 0, Space.World);
    }

    void DemonstrateAnimation()
    {
        if (targetChar == null) return;
        
        AnimationAssist animAssist = new AnimationAssist(targetChar.charInfo.animBody);
        if (animAssist.NowAnimationState.speed > 0)
        {
            Logger.LogInfo("Pausing animation");
            animAssist.Stop();
        }
        else
        {
            Logger.LogInfo("Resuming animation");
            animAssist.ReStart();
        }
    }

    void DemonstrateClothing()
    {
        if (targetChar == null) return;
        
        const int clothes_top = 0; // Top clothing slot ID
        byte currentState = targetChar.charInfo.fileStatus.clothesState[clothes_top];
        byte nextState = (currentState == 0) ? (byte)2 : (byte)0;
        targetChar.charInfo.fileStatus.clothesState[clothes_top] = nextState;
        targetChar.charInfo.Reload(false, false, true);
        Logger.LogInfo("Toggled top clothing state: " + (nextState == 0 ? "On" : "Off"));
    }

    void ToggleFirstPersonView()
    {
        if (targetChar == null) return;
        
        if (fpvInstance == null)
        {
            Logger.LogInfo("Enabling first-person view");
            GameObject controllerObj = new GameObject("FirstPersonController");
            fpvInstance = controllerObj.AddComponent<FirstPersonController>();
            fpvInstance.SetTarget(targetChar);
        }
        else
        {
            Logger.LogInfo("Disabling first-person view");
            Destroy(fpvInstance.gameObject);
            fpvInstance = null;
        }
    }
    
    // Add this method to fix the camera control enable/disable
    void OnApplicationQuit()
    {
        if (Studio.Studio.Instance != null && Studio.Studio.Instance.cameraCtrl != null)
        {
            Studio.Studio.Instance.cameraCtrl.enable = true;
        }
    }

    void DemonstrateInteraction()
    {
        if (targetChar == null) return;
        
        Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
        if (Physics.Raycast(ray, out RaycastHit hitInfo, 100f))
        {
            Logger.LogInfo("Ray hit: " + hitInfo.collider.name + ". Teleporting character.");
            targetChar.guideObject.transform.position = hitInfo.point;
        }
    }

    void ToggleIKAttachment()
    {
        if (targetChar == null) return;
        
        if (ikAttacherInstance == null)
        {
            Logger.LogInfo("Enabling IK attachment: Right hand follows red cube");
            objectToFollowIK.SetActive(true);
            objectToFollowIK.transform.position = targetChar.guideObject.transform.position + new Vector3(0.5f, 1f, 0.5f);

            GameObject attacherObj = new GameObject("IKAttacher");
            ikAttacherInstance = attacherObj.AddComponent<IKAttacher>();
            ikAttacherInstance.Attach(targetChar, 2, objectToFollowIK.transform); // 2 = right hand IK
        }
        else
        {
            Logger.LogInfo("Disabling IK attachment");
            objectToFollowIK.SetActive(false);
            Destroy(ikAttacherInstance.gameObject);
            ikAttacherInstance = null;
        }
    }
}

// First-person controller based on tutorial
public class FirstPersonController : MonoBehaviour
{
    private OCIChar targetChar;
    private Transform headBone;
    private Transform charTransform;
    private Camera mainCamera;
    public float moveSpeed = 2.0f;

    public void SetTarget(OCIChar ociChar)
    {
        targetChar = ociChar;
        charTransform = ociChar.guideObject.transform;
        
        // Find head bone (female: cf_J_Head, male: cm_J_Head)
        headBone = ociChar.charInfo.animBody.transform.Find("cf_J_Head");
        if (headBone == null) headBone = ociChar.charInfo.animBody.transform.Find("cm_J_Head");
        
        mainCamera = Camera.main;
        if (mainCamera != null) 
            Studio.Studio.Instance.cameraCtrl.enable = false;
    }

    void LateUpdate()
    {
        if (targetChar == null || headBone == null || mainCamera == null) return;
        
        // Position camera at head
        mainCamera.transform.position = headBone.position;
        mainCamera.transform.rotation = headBone.rotation;

        // Movement controls
        float moveHorizontal = Input.GetAxis("Horizontal");
        float moveVertical = Input.GetAxis("Vertical");
        Vector3 moveDirection = charTransform.forward * moveVertical + charTransform.right * moveHorizontal;
        charTransform.position += moveDirection.normalized * moveSpeed * Time.deltaTime;
    }

    void OnDestroy()
    {
        if (Studio.Studio.Instance != null && Studio.Studio.Instance.cameraCtrl != null)
            Studio.Studio.Instance.cameraCtrl.enable = true;
    }
}

// IK attachment controller based on tutorial
public class IKAttacher : MonoBehaviour
{
    private OCIChar.IKInfo targetIK;
    private Transform objectToFollow;

    public void Attach(OCIChar ociChar, int ikSlot, Transform targetObject)
    {
        if (ikSlot >= ociChar.ikInfo.Length) return;
        
        targetIK = ociChar.ikInfo[ikSlot];
        objectToFollow = targetObject;
        targetIK.active = true;
        
        // Fix for IK target info access
        if (targetIK.oiIKTargetInfo != null)
        {
            targetIK.oiIKTargetInfo.guideObject.transform.position = targetObject.position;
        }
    }

    void LateUpdate()
    {
        if (targetIK != null && targetIK.active && objectToFollow != null)
        {
            // Corrected property name for IK target info
            if (targetIK.oiIKTargetInfo != null)
            {
                Transform ikTargetTransform = targetIK.oiIKTargetInfo.guideObject.transform;
                ikTargetTransform.position = objectToFollow.position;
                ikTargetTransform.rotation = objectToFollow.rotation;
            }
        }
        else
        {
            Destroy(gameObject);
        }
    }

    void OnDestroy()
    {
        if (targetIK != null) 
            targetIK.active = false;
    }
}
