using BepInEx;
using BepInEx.Logging;
using UnityEngine;
using System.Collections.Generic;
using Mono.Cecil;

namespace MyFirstPlugin
{
    [BepInPlugin("com.yourname.characterinfoui", "CharacterInfoUI", "1.0.0")]
    public class Plugin : BaseUnityPlugin
    {
        private bool showUI = false;
        private UIDesigner uiDesigner;
        
        public static Vector3 mousePos;
        private void Awake()
        {
            // 初始化UIDesigner实例
            uiDesigner = new UIDesigner(this);
        }

        private void Update()
        {
            if (Input.GetKeyDown(KeyCode.F3))
            {
                showUI = !showUI;

                GameObject character =GameObject.Find("CommonSpace/chaF_001");
                if (character!= null){
                    GUILayout.Label("Character Name: " + character.name +";"+character.transform.position.ToString());
                }
                else{
                    GUILayout.Label("Character Name: null");

                }
            }
            
            mousePos = Input.mousePosition;
        }

        private void OnGUI()
        {
            if (showUI)
            {
                GUI.Window(0, new Rect(10, 10, 200, 450), uiDesigner.DrawWindow, "My Plugin UI");
            }
        }
    }

    public class UIDesigner
    {
        private Plugin plugin;
        
        public UIDesigner(Plugin plugin)
        {
            // 构造函数中可以进行一些初始化操作
            this.plugin = plugin;
        }

        public void DrawWindow(int windowID)
        {
            if (GUI.Button(new Rect(10, 30, 180, 30), "Button 1"))
            {
                GUILayout.Label("Button 1 clicked");
            }
            GUILayout.Label("Mouse Position: " + Plugin.mousePos);
        }
    }
}