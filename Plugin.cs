using BepInEx;
using BepInEx.Logging;
using UnityEngine;
using System.Collections.Generic;
using System.Collections;
using System.Linq;
using Mono.Cecil;

namespace MyFirstPlugin
{
    [BepInPlugin("com.yourname.characterinfoui", "CharacterInfoUI", "1.0.0")]
    public class Plugin : BaseUnityPlugin
    {
        private bool showUI = false;
        private UIDesigner uiDesigner;

        public static Vector3 mousePos;
        private void Awake()
        {
            // 初始化UIDesigner实例
            uiDesigner = new UIDesigner(this);
        }

        private void Update()
        {
            if (Input.GetKeyDown(KeyCode.F3))
            {
                showUI = !showUI;
            }

            mousePos = Input.mousePosition;

            // 更新移动控制
            uiDesigner.UpdateMovement();
        }

        private void OnGUI()
        {
            if (showUI)
            {
                GUI.Window(0, new Rect(10, 10, 300, 600), uiDesigner.DrawWindow, "Character Control UI");
            }
        }
    }

    public class UIDesigner
    {
        private Plugin plugin;

        // 移动控制相关变量
        private bool isLinearMoving = false;
        private bool isCircularMoving = false;
        private bool isSquareMoving = false;
        private float moveSpeed = 2.0f;
        private float circleRadius = 3.0f;
        private float squareSize = 4.0f;

        // 移动状态变量
        private Vector3 startPosition;
        private Vector3 targetPosition;
        private float moveTimer = 0f;
        private int squareMoveStep = 0; // 0-3 对应方形的四条边

        // 当前选中的角色
        private object currentCharacter = null;

        public UIDesigner(Plugin plugin)
        {
            this.plugin = plugin;
        }

        public void DrawWindow(int windowID)
        {
            GUILayout.BeginVertical();

            // 角色选择区域
            GUILayout.Label("=== 角色控制 ===");
            if (GUILayout.Button("选择当前角色"))
            {
                SelectCurrentCharacter();
            }

            if (currentCharacter != null)
            {
                GUILayout.Label("角色已选中");
            }
            else
            {
                GUILayout.Label("未选中角色");
            }

            GUILayout.Space(10);

            // 移动控制区域
            GUILayout.Label("=== 移动控制 ===");

            // 移动速度控制
            GUILayout.Label($"移动速度: {moveSpeed:F1}");
            moveSpeed = GUILayout.HorizontalSlider(moveSpeed, 0.5f, 5.0f);

            GUILayout.Space(5);

            // 直线移动
            if (GUILayout.Button(isLinearMoving ? "停止直线移动" : "开始直线移动"))
            {
                ToggleLinearMovement();
            }

            // 圆形移动
            GUILayout.Label($"圆形半径: {circleRadius:F1}");
            circleRadius = GUILayout.HorizontalSlider(circleRadius, 1.0f, 5.0f);

            if (GUILayout.Button(isCircularMoving ? "停止圆形移动" : "开始圆形移动"))
            {
                ToggleCircularMovement();
            }

            // 方形移动
            GUILayout.Label($"方形大小: {squareSize:F1}");
            squareSize = GUILayout.HorizontalSlider(squareSize, 2.0f, 6.0f);

            if (GUILayout.Button(isSquareMoving ? "停止方形移动" : "开始方形移动"))
            {
                ToggleSquareMovement();
            }

            GUILayout.Space(10);

            // 服装控制区域
            GUILayout.Label("=== 服装控制 ===");

            if (GUILayout.Button("全部穿上"))
            {
                SetAllClothesState(0); // 穿
            }

            if (GUILayout.Button("全部半脱"))
            {
                SetAllClothesState(1); // 半脱
            }

            if (GUILayout.Button("全部脱掉"))
            {
                SetAllClothesState(2); // 脱
            }

            GUILayout.Space(10);

            // 调试信息
            GUILayout.Label("=== 调试信息 ===");
            GUILayout.Label($"鼠标位置: {Plugin.mousePos}");
            if (currentCharacter != null)
            {
                GUILayout.Label("移动状态: " + GetMovementStatus());
            }

            GUILayout.EndVertical();

            GUI.DragWindow();
        }

        // 选择当前角色
        private void SelectCurrentCharacter()
        {
            try
            {
                // 尝试获取Studio实例和角色
                var studioInstance = GetStudioInstance();
                if (studioInstance != null)
                {
                    var characters = GetAllCharacters(studioInstance);
                    if (characters != null && characters.Count > 0)
                    {
                        currentCharacter = characters.First();
                        startPosition = GetCharacterPosition(currentCharacter);
                        Debug.Log("角色选择成功");
                    }
                    else
                    {
                        Debug.Log("未找到角色");
                    }
                }
                else
                {
                    Debug.Log("未找到Studio实例");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError("选择角色时出错: " + e.Message);
            }
        }

        // 获取Studio实例
        private object GetStudioInstance()
        {
            try
            {
                var studioType = System.Type.GetType("Studio.Studio, Assembly-CSharp");
                if (studioType != null)
                {
                    var instanceProperty = studioType.GetProperty("Instance");
                    if (instanceProperty != null)
                    {
                        return instanceProperty.GetValue(null);
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError("获取Studio实例失败: " + e.Message);
            }
            return null;
        }

        // 获取所有角色
        private List<object> GetAllCharacters(object studioInstance)
        {
            var characters = new List<object>();
            try
            {
                var dicObjectCtrlField = studioInstance.GetType().GetField("dicObjectCtrl");
                if (dicObjectCtrlField != null)
                {
                    var dicObjectCtrl = dicObjectCtrlField.GetValue(studioInstance);
                    if (dicObjectCtrl != null)
                    {
                        var dict = dicObjectCtrl as System.Collections.IDictionary;
                        if (dict != null)
                        {
                            foreach (var kvp in dict)
                            {
                                var objectCtrl = ((System.Collections.DictionaryEntry)kvp).Value;
                                if (IsOCIChar(objectCtrl))
                                {
                                    characters.Add(objectCtrl);
                                }
                            }
                        }
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError("获取角色列表失败: " + e.Message);
            }
            return characters;
        }

        // 判断是否为OCIChar类型
        private bool IsOCIChar(object obj)
        {
            if (obj == null) return false;
            return obj.GetType().Name.Contains("OCIChar");
        }

        // 获取角色位置
        private Vector3 GetCharacterPosition(object character)
        {
            try
            {
                var oiCharInfoField = character.GetType().GetField("oiCharInfo");
                if (oiCharInfoField != null)
                {
                    var oiCharInfo = oiCharInfoField.GetValue(character);
                    if (oiCharInfo != null)
                    {
                        var changeAmountField = oiCharInfo.GetType().GetField("changeAmount");
                        if (changeAmountField != null)
                        {
                            var changeAmount = changeAmountField.GetValue(oiCharInfo);
                            if (changeAmount != null)
                            {
                                var posField = changeAmount.GetType().GetField("pos");
                                if (posField != null)
                                {
                                    return (Vector3)posField.GetValue(changeAmount);
                                }
                            }
                        }
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError("获取角色位置失败: " + e.Message);
            }
            return Vector3.zero;
        }

        // 设置角色位置
        private void SetCharacterPosition(object character, Vector3 position)
        {
            try
            {
                var oiCharInfoField = character.GetType().GetField("oiCharInfo");
                if (oiCharInfoField != null)
                {
                    var oiCharInfo = oiCharInfoField.GetValue(character);
                    if (oiCharInfo != null)
                    {
                        var changeAmountField = oiCharInfo.GetType().GetField("changeAmount");
                        if (changeAmountField != null)
                        {
                            var changeAmount = changeAmountField.GetValue(oiCharInfo);
                            if (changeAmount != null)
                            {
                                var posField = changeAmount.GetType().GetField("pos");
                                if (posField != null)
                                {
                                    posField.SetValue(changeAmount, position);
                                }
                            }
                        }
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError("设置角色位置失败: " + e.Message);
            }
        }

        // 切换直线移动
        private void ToggleLinearMovement()
        {
            if (currentCharacter == null) return;

            isLinearMoving = !isLinearMoving;
            if (isLinearMoving)
            {
                // 停止其他移动
                isCircularMoving = false;
                isSquareMoving = false;

                startPosition = GetCharacterPosition(currentCharacter);
                targetPosition = startPosition + Vector3.forward * 5f; // 向前移动5单位
                moveTimer = 0f;
            }
        }

        // 切换圆形移动
        private void ToggleCircularMovement()
        {
            if (currentCharacter == null) return;

            isCircularMoving = !isCircularMoving;
            if (isCircularMoving)
            {
                // 停止其他移动
                isLinearMoving = false;
                isSquareMoving = false;

                startPosition = GetCharacterPosition(currentCharacter);
                moveTimer = 0f;
            }
        }

        // 切换方形移动
        private void ToggleSquareMovement()
        {
            if (currentCharacter == null) return;

            isSquareMoving = !isSquareMoving;
            if (isSquareMoving)
            {
                // 停止其他移动
                isLinearMoving = false;
                isCircularMoving = false;

                startPosition = GetCharacterPosition(currentCharacter);
                squareMoveStep = 0;
                moveTimer = 0f;
            }
        }

        // 更新移动
        public void UpdateMovement()
        {
            if (currentCharacter == null) return;

            if (isLinearMoving)
            {
                UpdateLinearMovement();
            }
            else if (isCircularMoving)
            {
                UpdateCircularMovement();
            }
            else if (isSquareMoving)
            {
                UpdateSquareMovement();
            }
        }

        // 更新直线移动
        private void UpdateLinearMovement()
        {
            moveTimer += Time.deltaTime * moveSpeed;

            // 来回移动
            float t = Mathf.PingPong(moveTimer, 1f);
            Vector3 newPosition = Vector3.Lerp(startPosition, targetPosition, t);
            SetCharacterPosition(currentCharacter, newPosition);
        }

        // 更新圆形移动
        private void UpdateCircularMovement()
        {
            moveTimer += Time.deltaTime * moveSpeed;

            float angle = moveTimer;
            Vector3 offset = new Vector3(
                Mathf.Cos(angle) * circleRadius,
                0,
                Mathf.Sin(angle) * circleRadius
            );

            Vector3 newPosition = startPosition + offset;
            SetCharacterPosition(currentCharacter, newPosition);
        }

        // 更新方形移动
        private void UpdateSquareMovement()
        {
            moveTimer += Time.deltaTime * moveSpeed;

            if (moveTimer >= 1f)
            {
                moveTimer = 0f;
                squareMoveStep = (squareMoveStep + 1) % 4;
            }

            Vector3 offset = Vector3.zero;
            float t = moveTimer;

            switch (squareMoveStep)
            {
                case 0: // 向右
                    offset = Vector3.Lerp(Vector3.zero, Vector3.right * squareSize, t);
                    break;
                case 1: // 向前
                    offset = Vector3.right * squareSize + Vector3.Lerp(Vector3.zero, Vector3.forward * squareSize, t);
                    break;
                case 2: // 向左
                    offset = Vector3.right * squareSize + Vector3.forward * squareSize + Vector3.Lerp(Vector3.zero, Vector3.left * squareSize, t);
                    break;
                case 3: // 向后
                    offset = Vector3.forward * squareSize + Vector3.Lerp(Vector3.zero, Vector3.back * squareSize, t);
                    break;
            }

            Vector3 newPosition = startPosition + offset;
            SetCharacterPosition(currentCharacter, newPosition);
        }

        // 设置所有服装状态
        private void SetAllClothesState(int state)
        {
            if (currentCharacter == null) return;

            try
            {
                // 获取角色的SetClothesState方法
                var method = currentCharacter.GetType().GetMethod("SetClothesState");
                if (method != null)
                {
                    // 设置所有服装类型 (0-7)
                    for (int i = 0; i <= 7; i++)
                    {
                        method.Invoke(currentCharacter, new object[] { i, state });
                    }
                    Debug.Log($"设置所有服装状态为: {state}");
                }
                else
                {
                    Debug.LogError("未找到SetClothesState方法");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError("设置服装状态失败: " + e.Message);
            }
        }

        // 获取移动状态
        private string GetMovementStatus()
        {
            if (isLinearMoving) return "直线移动中";
            if (isCircularMoving) return "圆形移动中";
            if (isSquareMoving) return "方形移动中";
            return "静止";
        }
    }
}