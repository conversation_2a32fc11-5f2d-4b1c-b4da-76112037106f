using BepInEx;
using BepInEx.Logging;
using Studio;
using UnityEngine;
using System;
using System.Collections.Generic;

[BepInPlugin("com.yourname.charactercontrol", "Character Control Plugin", "1.0.0")]
public class CharacterControlPlugin : BaseUnityPlugin
{
    private ManualLogSource Logger;

    // UI控制
    private bool _showUI = false;

    // 角色控制
    private OCIChar _selectedCharacter = null;
    private List<OCIChar> _cachedCharacters = new List<OCIChar>();

    // 移动控制变量
    private bool _isLinearMoving = false;
    private bool _isCircularMoving = false;
    private bool _isSquareMoving = false;
    private bool _isFirstPersonMode = false;

    private float _moveSpeed = 2.0f;
    private float _circleRadius = 3.0f;
    private float _squareSize = 4.0f;
    private float _firstPersonMoveSpeed = 3.0f;

    // 移动状态变量
    private Vector3 _startPosition;
    private Vector3 _targetPosition;
    private float _moveTimer = 0f;
    private int _squareMoveStep = 0;

    // 第一人称相机控制
    private FirstPersonController _fpController = null;

    // 服装控制 - 基于教程文档的正确ID
    private static readonly int[] ClothesIds = {0, 1, 2, 3, 4, 5, 6, 7};

    void Awake()
    {
        Logger = base.Logger;
        Logger.LogInfo("Character Control Plugin loaded!");
    }

    void Start()
    {
        // 初始化角色缓存
        CacheCharacters();
    }

    void Update()
    {
        // 检查UI开关
        if (Input.GetKeyDown(KeyCode.F3))
        {
            _showUI = !_showUI;
            Logger.LogInfo($"UI显示状态: {_showUI}");
        }

        // 每30帧刷新一次角色缓存
        if (Time.frameCount % 30 == 0)
        {
            CacheCharacters();

            // 自动选择第一个角色（如果没有选中角色）
            if (_selectedCharacter == null && _cachedCharacters.Count > 0)
            {
                _selectedCharacter = _cachedCharacters[0];
                Logger.LogInfo($"自动选择角色: {_selectedCharacter.oiCharInfo}");
            }
        }

        // 更新移动控制
        UpdateMovement();
    }

    void OnGUI()
    {
        if (!_showUI) return;

        GUI.Window(0, new Rect(10, 10, 350, 700), DrawUI, "Character Control UI");
    }

    void DrawUI(int windowID)
    {
        GUILayout.BeginVertical();

        // 角色状态显示
        GUILayout.Label("=== 角色状态 ===");
        if (_selectedCharacter != null)
        {
            GUILayout.Label("角色已选中");
            GUILayout.Label("位置: " + _selectedCharacter.guideObject.changeAmount.pos.ToString("F2"));
        }
        else
        {
            GUILayout.Label("正在搜索角色...");
            if (GUILayout.Button("手动刷新角色"))
            {
                CacheCharacters();
            }
        }

        GUILayout.Space(10);

        // 移动控制区域
        GUILayout.Label("=== 移动控制 ===");

        if (_selectedCharacter != null)
        {
            // 移动速度控制
            GUILayout.Label($"移动速度: {_moveSpeed:F1}");
            _moveSpeed = GUILayout.HorizontalSlider(_moveSpeed, 0.5f, 5.0f);

            GUILayout.Space(5);

            // 直线移动
            if (GUILayout.Button(_isLinearMoving ? "停止直线移动" : "开始直线移动"))
            {
                ToggleLinearMovement();
            }

            // 圆形移动
            GUILayout.Label($"圆形半径: {_circleRadius:F1}");
            _circleRadius = GUILayout.HorizontalSlider(_circleRadius, 1.0f, 5.0f);

            if (GUILayout.Button(_isCircularMoving ? "停止圆形移动" : "开始圆形移动"))
            {
                ToggleCircularMovement();
            }

            // 方形移动
            GUILayout.Label($"方形大小: {_squareSize:F1}");
            _squareSize = GUILayout.HorizontalSlider(_squareSize, 2.0f, 6.0f);

            if (GUILayout.Button(_isSquareMoving ? "停止方形移动" : "开始方形移动"))
            {
                ToggleSquareMovement();
            }

            GUILayout.Space(10);

            // 第一人称模式
            GUILayout.Label("=== 第一人称模式 ===");

            if (GUILayout.Button(_isFirstPersonMode ? "退出第一人称" : "进入第一人称"))
            {
                ToggleFirstPersonMode();
            }

            if (_isFirstPersonMode)
            {
                GUILayout.Label($"移动速度: {_firstPersonMoveSpeed:F1}");
                _firstPersonMoveSpeed = GUILayout.HorizontalSlider(_firstPersonMoveSpeed, 1.0f, 10.0f);
                GUILayout.Label("WASD键移动角色");
                GUILayout.Label("鼠标控制视角");
            }

            GUILayout.Space(10);

            // 服装控制区域
            GUILayout.Label("=== 服装控制 ===");

            if (GUILayout.Button("全部穿上"))
            {
                SetClothesState(0);
            }

            if (GUILayout.Button("全部半脱"))
            {
                SetClothesState(1);
            }

            if (GUILayout.Button("全部脱掉"))
            {
                SetClothesState(2);
            }

            GUILayout.Space(10);

            // 状态显示
            GUILayout.Label("=== 状态信息 ===");
            GUILayout.Label("移动状态: " + GetMovementStatus());
        }
        else
        {
            GUILayout.Label("请等待角色加载...");
        }

        GUILayout.EndVertical();
        GUI.DragWindow();
    }

    // 更新移动控制
    void UpdateMovement()
    {
        if (_selectedCharacter == null) return;

        if (_isFirstPersonMode)
        {
            UpdateFirstPersonMovement();
        }
        else if (_isLinearMoving)
        {
            UpdateLinearMovement();
        }
        else if (_isCircularMoving)
        {
            UpdateCircularMovement();
        }
        else if (_isSquareMoving)
        {
            UpdateSquareMovement();
        }
    }

    // 切换直线移动
    void ToggleLinearMovement()
    {
        if (_selectedCharacter == null) return;

        _isLinearMoving = !_isLinearMoving;
        if (_isLinearMoving)
        {
            // 停止其他移动
            _isCircularMoving = false;
            _isSquareMoving = false;
            _isFirstPersonMode = false;

            _startPosition = _selectedCharacter.guideObject.changeAmount.pos;
            _targetPosition = _startPosition + Vector3.forward * 5f;
            _moveTimer = 0f;
        }
    }

    // 切换圆形移动
    void ToggleCircularMovement()
    {
        if (_selectedCharacter == null) return;

        _isCircularMoving = !_isCircularMoving;
        if (_isCircularMoving)
        {
            // 停止其他移动
            _isLinearMoving = false;
            _isSquareMoving = false;
            _isFirstPersonMode = false;

            _startPosition = _selectedCharacter.guideObject.changeAmount.pos;
            _moveTimer = 0f;
        }
    }

    // 切换方形移动
    void ToggleSquareMovement()
    {
        if (_selectedCharacter == null) return;

        _isSquareMoving = !_isSquareMoving;
        if (_isSquareMoving)
        {
            // 停止其他移动
            _isLinearMoving = false;
            _isCircularMoving = false;
            _isFirstPersonMode = false;

            _startPosition = _selectedCharacter.guideObject.changeAmount.pos;
            _squareMoveStep = 0;
            _moveTimer = 0f;
        }
    }

    // 切换第一人称模式
    void ToggleFirstPersonMode()
    {
        if (_selectedCharacter == null) return;

        _isFirstPersonMode = !_isFirstPersonMode;
        if (_isFirstPersonMode)
        {
            // 停止其他移动
            _isLinearMoving = false;
            _isCircularMoving = false;
            _isSquareMoving = false;

            // 创建第一人称控制器
            if (_fpController == null)
            {
                GameObject controllerObj = new GameObject("FirstPersonController");
                _fpController = controllerObj.AddComponent<FirstPersonController>();
                _fpController.SetTarget(_selectedCharacter);
            }

            Logger.LogInfo("进入第一人称模式");
        }
        else
        {
            // 销毁第一人称控制器
            if (_fpController != null)
            {
                Destroy(_fpController.gameObject);
                _fpController = null;
            }

            Logger.LogInfo("退出第一人称模式");
        }
    }

    // 更新直线移动
    void UpdateLinearMovement()
    {
        _moveTimer += Time.deltaTime * _moveSpeed;
        float t = Mathf.PingPong(_moveTimer, 1f);
        Vector3 newPosition = Vector3.Lerp(_startPosition, _targetPosition, t);
        _selectedCharacter.guideObject.changeAmount.pos = newPosition;
    }

    // 更新圆形移动
    void UpdateCircularMovement()
    {
        _moveTimer += Time.deltaTime * _moveSpeed;
        float angle = _moveTimer;
        Vector3 offset = new Vector3(
            Mathf.Cos(angle) * _circleRadius,
            0,
            Mathf.Sin(angle) * _circleRadius
        );
        _selectedCharacter.guideObject.changeAmount.pos = _startPosition + offset;
    }

    // 更新方形移动
    void UpdateSquareMovement()
    {
        _moveTimer += Time.deltaTime * _moveSpeed;

        if (_moveTimer >= 1f)
        {
            _moveTimer = 0f;
            _squareMoveStep = (_squareMoveStep + 1) % 4;
        }

        Vector3 offset = Vector3.zero;
        float t = _moveTimer;

        switch (_squareMoveStep)
        {
            case 0: // 向右
                offset = Vector3.Lerp(Vector3.zero, Vector3.right * _squareSize, t);
                break;
            case 1: // 向前
                offset = Vector3.right * _squareSize + Vector3.Lerp(Vector3.zero, Vector3.forward * _squareSize, t);
                break;
            case 2: // 向左
                offset = Vector3.right * _squareSize + Vector3.forward * _squareSize + Vector3.Lerp(Vector3.zero, Vector3.left * _squareSize, t);
                break;
            case 3: // 向后
                offset = Vector3.forward * _squareSize + Vector3.Lerp(Vector3.zero, Vector3.back * _squareSize, t);
                break;
        }

        _selectedCharacter.guideObject.changeAmount.pos = _startPosition + offset;
    }

    // 更新第一人称移动
    void UpdateFirstPersonMovement()
    {
        if (_selectedCharacter == null) return;

        Vector3 moveDirection = Vector3.zero;

        // WASD控制
        if (Input.GetKey(KeyCode.W)) // 前进
        {
            moveDirection += Vector3.forward;
        }
        if (Input.GetKey(KeyCode.S)) // 后退
        {
            moveDirection += Vector3.back;
        }
        if (Input.GetKey(KeyCode.A)) // 左移
        {
            moveDirection += Vector3.left;
        }
        if (Input.GetKey(KeyCode.D)) // 右移
        {
            moveDirection += Vector3.right;
        }

        // 应用移动
        if (moveDirection != Vector3.zero)
        {
            moveDirection.Normalize();
            Vector3 currentPos = _selectedCharacter.guideObject.changeAmount.pos;
            Vector3 newPosition = currentPos + moveDirection * _firstPersonMoveSpeed * Time.deltaTime;
            _selectedCharacter.guideObject.changeAmount.pos = newPosition;
        }
    }

    // 设置服装状态 - 基于教程文档的正确方法
    void SetClothesState(int state)
    {
        if (_selectedCharacter == null) return;

        try
        {
            // 使用charInfo.SetClothesState方法，这是正确的API
            foreach (var id in ClothesIds)
            {
                _selectedCharacter.charInfo.SetClothesState(id, (byte)state, true);
            }
            Logger.LogInfo($"设置所有服装状态为: {state}");
        }
        catch (Exception e)
        {
            Logger.LogError("设置服装状态失败: " + e.Message);
        }
    }

    // 获取移动状态
    string GetMovementStatus()
    {
        if (_isFirstPersonMode) return "第一人称模式";
        if (_isLinearMoving) return "直线移动中";
        if (_isCircularMoving) return "圆形移动中";
        if (_isSquareMoving) return "方形移动中";
        return "静止";
    }

    void CacheCharacters()
    {
        if (Studio.Studio.Instance == null) return;
        _cachedCharacters.Clear();
        foreach (var obj in Studio.Studio.Instance.dicObjectCtrl.Values)
        {
            if (obj is OCIChar ociChar)
            {
                _cachedCharacters.Add(ociChar);
            }
        }
    }
}

// 第一人称控制器类 - 基于教程文档
public class FirstPersonController : MonoBehaviour
{
    private OCIChar targetChar;
    private Transform headBone;
    private Transform charTransform;
    private Camera mainCamera;
    public float moveSpeed = 2.0f;

    public void SetTarget(OCIChar ociChar)
    {
        targetChar = ociChar;
        charTransform = ociChar.guideObject.transform;

        // 根据教程文档，查找头部骨骼
        headBone = ociChar.charInfo.animBody.transform.Find("cf_J_Head");
        if (headBone == null)
            headBone = ociChar.charInfo.animBody.transform.Find("cm_J_Head");

        mainCamera = Camera.main;
    }

    void LateUpdate()
    {
        if (targetChar == null || mainCamera == null) return;

        // 简化的第一人称控制 - 直接控制角色移动
        float moveHorizontal = Input.GetAxis("Horizontal");
        float moveVertical = Input.GetAxis("Vertical");
        Vector3 moveDirection = charTransform.forward * moveVertical + charTransform.right * moveHorizontal;
        charTransform.position += moveDirection.normalized * moveSpeed * Time.deltaTime;

        // 如果有头部骨骼，设置相机位置
        if (headBone != null)
        {
            mainCamera.transform.position = headBone.position + Vector3.up * 0.1f;
            mainCamera.transform.rotation = headBone.rotation;
        }
    }

    void OnDestroy()
    {
        // 清理资源
    }
}