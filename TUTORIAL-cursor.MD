# HS2 Studio 开发教程

## 目录
1. [基础概念](#基础概念)
2. [人物控制](#人物控制)
3. [相机系统](#相机系统)
4. [IK/FK系统](#ikfk系统)
5. [动画系统](#动画系统)
6. [衣物系统](#衣物系统)
7. [物品系统](#物品系统)
8. [第一人称视角](#第一人称视角)
9. [交互系统](#交互系统)
10. [实用示例](#实用示例)

---

## 基础概念

### 核心类结构

HS2 Studio的核心类结构如下：

```csharp
// 主要管理器
Studio - 主管理器，控制整个Studio系统
GuideObjectManager - 引导对象管理器
TreeNodeCtrl - 树节点控制器

// 对象控制信息
ObjectCtrlInfo - 对象控制信息基类
OCIChar - 人物对象控制信息
OCIItem - 物品对象控制信息
OCILight - 灯光对象控制信息

// 信息类
OICharInfo - 人物信息
OIItemInfo - 物品信息
OILightInfo - 灯光信息
```

### 获取主要实例

```csharp
// 获取Studio主实例
Studio studio = Singleton<Studio>.Instance;

// 获取相机控制器
CameraControl cameraCtrl = studio.cameraCtrl;

// 获取当前选中的对象
ObjectCtrlInfo selectedObject = studio.treeNodeCtrl.selectObjectCtrl;
```

---

## 人物控制

### 获取人物实例

```csharp
// 获取所有人物
List<ObjectCtrlInfo> allChars = Studio.GetCtrlInfo<OCIChar>();

// 获取选中的人物
OCIChar selectedChar = Studio.treeNodeCtrl.selectObjectCtrl as OCIChar;

// 通过索引获取人物
OCIChar charByIndex = Studio.GetCtrlInfo(index) as OCIChar;
```

### 人物位置和旋转

```csharp
// 设置人物位置
OCIChar char = GetSelectedCharacter();
char.guideObject.changeAmount.pos = new Vector3(1.0f, 0.0f, 2.0f);
char.guideObject.changeAmount.rot = new Vector3(0.0f, 90.0f, 0.0f);

// 获取人物位置
Vector3 position = char.guideObject.changeAmount.pos;
Vector3 rotation = char.guideObject.changeAmount.rot;

// 直接设置Transform
char.guideObject.transformTarget.position = new Vector3(1.0f, 0.0f, 2.0f);
char.guideObject.transformTarget.rotation = Quaternion.Euler(0.0f, 90.0f, 0.0f);
```

### 人物状态控制

```csharp
// 设置人物可见性
char.OnVisible(true); // 显示
char.OnVisible(false); // 隐藏

// 设置人物选择状态
char.OnSelect(true); // 选中
char.OnSelect(false); // 取消选中

// 获取人物性别
int sex = char.sex; // 0=男性, 1=女性

// 获取人物信息
ChaControl chaControl = char.charInfo;
ChaFileStatus fileStatus = char.charFileStatus;
```

### 人物表情控制

```csharp
// 启用表情类别
char.EnableExpressionCategory(0, true); // 启用第一个表情类别
char.EnableExpressionCategory(1, false); // 禁用第二个表情类别

// 设置表情参数
char.charInfo.fileStatus.expression[0] = 0.5f; // 设置第一个表情强度
char.charInfo.fileStatus.expression[1] = 0.8f; // 设置第二个表情强度
```

---

## 相机系统

### 相机控制基础

```csharp
// 获取相机控制器
CameraControl cameraCtrl = Singleton<Studio>.Instance.cameraCtrl;

// 设置相机位置
cameraCtrl.cameraData.pos = new Vector3(0.0f, 1.5f, -3.0f);

// 设置相机旋转
cameraCtrl.cameraData.rotate = new Vector3(15.0f, 0.0f, 0.0f);

// 设置相机距离
cameraCtrl.cameraData.distance = new Vector3(0.0f, 0.0f, -5.0f);

// 设置视野角度
cameraCtrl.fieldOfView = 60.0f;
```

### 相机目标设置

```csharp
// 设置相机目标为选中的人物
OCIChar targetChar = GetSelectedCharacter();
if (targetChar != null)
{
    cameraCtrl.TargetSet(targetChar.guideObject.transformTarget, true);
}

// 重置相机到默认位置
cameraCtrl.Reset(0);

// 设置相机为自由模式
cameraCtrl.cameraType = BaseCameraControl.Config.Rotation;
```

### 相机移动控制

```csharp
// 相机平移
cameraCtrl.cameraData.pos += new Vector3(0.1f, 0.0f, 0.0f); // 向右移动
cameraCtrl.cameraData.pos += new Vector3(0.0f, 0.1f, 0.0f); // 向上移动
cameraCtrl.cameraData.pos += new Vector3(0.0f, 0.0f, 0.1f); // 向前移动

// 相机旋转
cameraCtrl.cameraData.rotate.y += 5.0f; // 水平旋转
cameraCtrl.cameraData.rotate.x += 5.0f; // 垂直旋转

// 相机缩放
cameraCtrl.cameraData.distance.z -= 0.1f; // 拉近
cameraCtrl.cameraData.distance.z += 0.1f; // 推远
```

---

## IK/FK系统

### IK系统控制

```csharp
// 启用IK模式
char.ActiveKinematicMode(OICharInfo.KinematicMode.IK, true, true);

// 启用特定部位的IK
char.ActiveIK(OIBoneInfo.BoneGroup.Body, true, false);
char.ActiveIK(OIBoneInfo.BoneGroup.LeftArm, true, false);
char.ActiveIK(OIBoneInfo.BoneGroup.RightArm, true, false);
char.ActiveIK(OIBoneInfo.BoneGroup.LeftLeg, true, false);
char.ActiveIK(OIBoneInfo.BoneGroup.RightLeg, true, false);

// 获取IK目标信息
foreach (OCIChar.IKInfo ikInfo in char.listIKTarget)
{
    // 设置IK目标位置
    ikInfo.targetObject.position = new Vector3(1.0f, 0.5f, 0.0f);
    
    // 设置IK目标旋转
    ikInfo.targetObject.rotation = Quaternion.Euler(0.0f, 45.0f, 0.0f);
    
    // 复制骨骼位置到IK目标
    ikInfo.CopyBone();
    
    // 复制骨骼旋转到IK目标
    ikInfo.CopyBoneRotation();
}
```

### FK系统控制

```csharp
// 启用FK模式
char.ActiveKinematicMode(OICharInfo.KinematicMode.FK, true, true);

// 启用特定部位的FK
char.ActiveFK(OIBoneInfo.BoneGroup.Body, true, false);
char.ActiveFK(OIBoneInfo.BoneGroup.Neck, true, false);
char.ActiveFK(OIBoneInfo.BoneGroup.LeftHand, true, false);
char.ActiveFK(OIBoneInfo.BoneGroup.RightHand, true, false);
char.ActiveFK(OIBoneInfo.BoneGroup.Hair, true, false);
char.ActiveFK(OIBoneInfo.BoneGroup.Breast, true, false);
char.ActiveFK(OIBoneInfo.BoneGroup.Skirt, true, false);

// 获取FK骨骼信息
foreach (OCIChar.BoneInfo boneInfo in char.listBones)
{
    // 设置骨骼位置
    boneInfo.guideObject.changeAmount.pos = new Vector3(0.0f, 0.1f, 0.0f);
    
    // 设置骨骼旋转
    boneInfo.guideObject.changeAmount.rot = new Vector3(0.0f, 0.0f, 15.0f);
    
    // 复制骨骼位置
    boneInfo.guideObject.CopyBone();
}
```

### 骨骼组枚举

```csharp
public enum BoneGroup
{
    Body = 1,
    LeftArm = 2,
    RightArm = 4,
    LeftLeg = 8,
    RightLeg = 16,
    LeftHand = 32,
    RightHand = 64,
    Neck = 128,
    Hair = 256,
    Breast = 512,
    Skirt = 1024
}
```

---

## 动画系统

### 动画控制

```csharp
// 播放动画
char.charAnimeCtrl.Play("animation_name");

// 播放动画并设置时间
char.charAnimeCtrl.Play("animation_name", 0.5f);

// 播放动画并设置层
char.charAnimeCtrl.Play("animation_name", 0.0f, 1);

// 设置动画速度
char.animeSpeed = 1.5f; // 1.5倍速

// 获取当前动画时间
float normalizedTime = char.charAnimeCtrl.normalizedTime;

// 设置动画参数
char.animeOptionParam1 = 0.5f;
char.animeOptionParam2 = 0.8f;
```

### 动画信息

```csharp
// 设置动画信息
char.oiCharInfo.animeInfo.group = 0;    // 动画组
char.oiCharInfo.animeInfo.category = 1; // 动画类别
char.oiCharInfo.animeInfo.no = 5;       // 动画编号

// 加载动画
char.LoadAnime(0, 1, 5, 0.0f);

// 获取动画状态信息
AnimatorStateInfo stateInfo = char.charAnimeCtrl.animator.GetCurrentAnimatorStateInfo(0);
string stateName = stateInfo.IsName("animation_name");
```

### 动画事件

```csharp
// 监听动画事件
char.charAnimeCtrl.animator.GetBehaviour<AnimatorStateInfo>();

// 设置动画触发器
char.charAnimeCtrl.animator.SetTrigger("trigger_name");

// 设置动画布尔值
char.charAnimeCtrl.animator.SetBool("bool_name", true);

// 设置动画浮点值
char.charAnimeCtrl.animator.SetFloat("float_name", 0.5f);
```

---

## 衣物系统

### 衣物状态控制

```csharp
// 设置衣物状态
// 0=着衣, 1=半脱, 2=脱衣
char.charInfo.SetClothesState(0, 2, true); // 上衣脱衣
char.charInfo.SetClothesState(1, 1, true); // 下装半脱
char.charInfo.SetClothesState(2, 0, true); // 内衣着衣

// 获取衣物状态
byte topState = char.charFileStatus.clothesState[0]; // 上衣状态
byte bottomState = char.charFileStatus.clothesState[1]; // 下装状态
byte innerTopState = char.charFileStatus.clothesState[2]; // 内衣上衣状态
byte innerBottomState = char.charFileStatus.clothesState[3]; // 内衣下装状态
byte glovesState = char.charFileStatus.clothesState[4]; // 手套状态
byte pantyState = char.charFileStatus.clothesState[5]; // 内裤状态
byte socksState = char.charFileStatus.clothesState[6]; // 袜子状态
byte shoesState = char.charFileStatus.clothesState[7]; // 鞋子状态
```

### 衣物种类枚举

```csharp
public enum ClothesKind
{
    top = 0,        // 上衣
    bot = 1,        // 下装
    inner_t = 2,    // 内衣上衣
    inner_b = 3,    // 内衣下装
    gloves = 4,     // 手套
    pantst = 5,     // 内裤
    socks = 6,      // 袜子
    shoes = 7       // 鞋子
}
```

### 衣物可见性控制

```csharp
// 检查是否有特定衣物
bool hasTop = char.charInfo.IsClothes(0);
bool hasShoes = char.charInfo.IsClothes(7);

// 检查衣物状态类型
bool hasClothesState = char.charInfo.IsClothesStateKind(0);
Dictionary<byte, string> stateTypes = char.charInfo.GetClothesStateKind(0);

// 设置衣物可见性
char.charInfo.visibleAll = true; // 显示所有衣物
char.charInfo.visibleAll = false; // 隐藏所有衣物
```

### 配饰控制

```csharp
// 设置配饰显示状态
char.charInfo.fileStatus.showAccessory[0] = true;  // 显示第一个配饰
char.charInfo.fileStatus.showAccessory[1] = false; // 隐藏第二个配饰

// 设置所有配饰显示状态
char.SetAccessoryStateAll(true);  // 显示所有配饰
char.SetAccessoryStateAll(false); // 隐藏所有配饰

// 设置特定配饰状态
char.SetAccessoryState(0, true);  // 显示第一个配饰
char.SetAccessoryState(1, false); // 隐藏第二个配饰
```

---

## 物品系统

### 添加物品

```csharp
// 添加物品到场景
OCIItem item = AddObjectItem.Add(group, category, no);

// 示例：添加一个椅子
OCIItem chair = AddObjectItem.Add(0, 0, 1); // 组0，类别0，编号1

// 设置物品位置
chair.guideObject.changeAmount.pos = new Vector3(1.0f, 0.0f, 2.0f);
chair.guideObject.changeAmount.rot = new Vector3(0.0f, 90.0f, 0.0f);
```

### 物品控制

```csharp
// 获取物品信息
OIItemInfo itemInfo = item.itemInfo;

// 设置物品可见性
item.OnVisible(true);  // 显示物品
item.OnVisible(false); // 隐藏物品

// 设置物品选择状态
item.OnSelect(true);   // 选中物品
item.OnSelect(false);  // 取消选中

// 删除物品
item.OnDelete();
```

### 物品动画

```csharp
// 检查物品是否有动画
bool hasAnimation = item.isAnime;

// 播放物品动画
if (item.animator != null)
{
    item.animator.Play("animation_name");
    item.animator.SetTrigger("trigger_name");
    item.animator.SetBool("bool_name", true);
    item.animator.SetFloat("float_name", 0.5f);
}
```

### 物品颜色控制

```csharp
// 检查物品是否可以改变颜色
bool canChangeColor = item.isChangeColor;

// 设置物品颜色
if (item.itemComponent != null)
{
    // 设置主颜色
    item.itemComponent.SetColor(0, Color.red);
    item.itemComponent.SetColor(1, Color.blue);
    item.itemComponent.SetColor(2, Color.green);
    
    // 设置玻璃颜色
    item.itemComponent.SetGlassColor(Color.white);
    
    // 设置发光颜色
    item.itemComponent.SetEmissionColor(Color.yellow);
    item.itemComponent.SetEmissionStrength(1.5f);
}
```

---

## 第一人称视角

### 创建第一人称相机

```csharp
public class FirstPersonCamera : MonoBehaviour
{
    private CameraControl originalCamera;
    private Camera firstPersonCamera;
    private OCIChar targetCharacter;
    
    void Start()
    {
        // 获取原始相机
        originalCamera = Singleton<Studio>.Instance.cameraCtrl;
        
        // 创建第一人称相机
        GameObject cameraObj = new GameObject("FirstPersonCamera");
        firstPersonCamera = cameraObj.AddComponent<Camera>();
        firstPersonCamera.fieldOfView = 60f;
        firstPersonCamera.nearClipPlane = 0.1f;
        firstPersonCamera.farClipPlane = 1000f;
        
        // 设置相机层级
        cameraObj.layer = LayerMask.NameToLayer("Studio/Camera");
        
        // 禁用原始相机
        originalCamera.gameObject.SetActive(false);
    }
    
    void Update()
    {
        if (targetCharacter != null)
        {
            // 设置相机位置到人物头部
            Transform headBone = targetCharacter.charInfo.GetBoneTransform("cf_J_Head");
            if (headBone != null)
            {
                firstPersonCamera.transform.position = headBone.position + Vector3.up * 0.1f;
                firstPersonCamera.transform.rotation = headBone.rotation;
            }
        }
    }
}
```

### 第一人称移动控制

```csharp
public class FirstPersonMovement : MonoBehaviour
{
    public float moveSpeed = 5.0f;
    public float mouseSensitivity = 2.0f;
    public float jumpForce = 5.0f;
    
    private CharacterController characterController;
    private Camera playerCamera;
    private float verticalRotation = 0f;
    private Vector3 playerVelocity;
    private bool isGrounded;
    
    void Start()
    {
        characterController = GetComponent<CharacterController>();
        playerCamera = GetComponentInChildren<Camera>();
        
        // 锁定鼠标
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
    }
    
    void Update()
    {
        HandleMovement();
        HandleMouseLook();
        HandleJump();
    }
    
    void HandleMovement()
    {
        // 获取输入
        float horizontal = Input.GetAxis("Horizontal");
        float vertical = Input.GetAxis("Vertical");
        
        // 计算移动方向
        Vector3 moveDirection = transform.right * horizontal + transform.forward * vertical;
        
        // 应用移动
        characterController.Move(moveDirection * moveSpeed * Time.deltaTime);
        
        // 重力
        isGrounded = characterController.isGrounded;
        if (isGrounded && playerVelocity.y < 0)
        {
            playerVelocity.y = -2f;
        }
        
        playerVelocity.y += Physics.gravity.y * Time.deltaTime;
        characterController.Move(playerVelocity * Time.deltaTime);
    }
    
    void HandleMouseLook()
    {
        // 获取鼠标输入
        float mouseX = Input.GetAxis("Mouse X") * mouseSensitivity;
        float mouseY = Input.GetAxis("Mouse Y") * mouseSensitivity;
        
        // 水平旋转
        transform.Rotate(Vector3.up * mouseX);
        
        // 垂直旋转
        verticalRotation -= mouseY;
        verticalRotation = Mathf.Clamp(verticalRotation, -90f, 90f);
        playerCamera.transform.localRotation = Quaternion.Euler(verticalRotation, 0f, 0f);
    }
    
    void HandleJump()
    {
        if (Input.GetButtonDown("Jump") && isGrounded)
        {
            playerVelocity.y = Mathf.Sqrt(jumpForce * -2f * Physics.gravity.y);
        }
    }
}
```

### 切换到第一人称视角

```csharp
public void SwitchToFirstPerson(OCIChar character)
{
    // 获取人物头部位置
    Transform headBone = character.charInfo.GetBoneTransform("cf_J_Head");
    if (headBone == null) return;
    
    // 创建第一人称相机
    GameObject fpCameraObj = new GameObject("FirstPersonCamera");
    Camera fpCamera = fpCameraObj.AddComponent<Camera>();
    
    // 设置相机属性
    fpCamera.fieldOfView = 60f;
    fpCamera.nearClipPlane = 0.1f;
    fpCamera.farClipPlane = 1000f;
    fpCamera.clearFlags = CameraClearFlags.Skybox;
    
    // 设置相机位置
    fpCameraObj.transform.position = headBone.position + Vector3.up * 0.1f;
    fpCameraObj.transform.rotation = headBone.rotation;
    
    // 添加移动控制脚本
    fpCameraObj.AddComponent<FirstPersonMovement>();
    
    // 禁用原始相机
    Singleton<Studio>.Instance.cameraCtrl.gameObject.SetActive(false);
}
```

---

## 交互系统

### 鼠标交互

```csharp
public class MouseInteraction : MonoBehaviour
{
    private Camera playerCamera;
    private OCIChar selectedCharacter;
    
    void Start()
    {
        playerCamera = Camera.main;
    }
    
    void Update()
    {
        if (Input.GetMouseButtonDown(0)) // 左键点击
        {
            HandleMouseClick();
        }
        
        if (Input.GetMouseButtonDown(1)) // 右键点击
        {
            HandleRightClick();
        }
    }
    
    void HandleMouseClick()
    {
        Ray ray = playerCamera.ScreenPointToRay(Input.mousePosition);
        RaycastHit hit;
        
        if (Physics.Raycast(ray, out hit))
        {
            // 检查是否点击到人物
            OCIChar character = hit.collider.GetComponentInParent<OCIChar>();
            if (character != null)
            {
                SelectCharacter(character);
            }
            
            // 检查是否点击到物品
            OCIItem item = hit.collider.GetComponentInParent<OCIItem>();
            if (item != null)
            {
                SelectItem(item);
            }
        }
    }
    
    void SelectCharacter(OCIChar character)
    {
        // 取消之前的选择
        if (selectedCharacter != null)
        {
            selectedCharacter.OnSelect(false);
        }
        
        // 选择新的人物
        selectedCharacter = character;
        selectedCharacter.OnSelect(true);
        
        // 显示人物信息
        ShowCharacterInfo(character);
    }
    
    void SelectItem(OCIItem item)
    {
        item.OnSelect(true);
        ShowItemInfo(item);
    }
}
```

### 键盘快捷键

```csharp
public class KeyboardShortcuts : MonoBehaviour
{
    void Update()
    {
        // 数字键1-9选择人物
        for (int i = 0; i < 9; i++)
        {
            if (Input.GetKeyDown(KeyCode.Alpha1 + i))
            {
                SelectCharacterByIndex(i);
            }
        }
        
        // 空格键切换动画
        if (Input.GetKeyDown(KeyCode.Space))
        {
            ToggleAnimation();
        }
        
        // R键重置相机
        if (Input.GetKeyDown(KeyCode.R))
        {
            ResetCamera();
        }
        
        // T键切换衣物状态
        if (Input.GetKeyDown(KeyCode.T))
        {
            ToggleClothesState();
        }
        
        // I键切换IK/FK模式
        if (Input.GetKeyDown(KeyCode.I))
        {
            ToggleIKMode();
        }
    }
    
    void SelectCharacterByIndex(int index)
    {
        List<ObjectCtrlInfo> characters = Studio.GetCtrlInfo<OCIChar>();
        if (index < characters.Count)
        {
            OCIChar character = characters[index] as OCIChar;
            if (character != null)
            {
                character.OnSelect(true);
            }
        }
    }
    
    void ToggleAnimation()
    {
        OCIChar selectedChar = GetSelectedCharacter();
        if (selectedChar != null)
        {
            // 切换动画播放/暂停
            if (selectedChar.charAnimeCtrl.animator.enabled)
            {
                selectedChar.charAnimeCtrl.animator.enabled = false;
            }
            else
            {
                selectedChar.charAnimeCtrl.animator.enabled = true;
            }
        }
    }
    
    void ResetCamera()
    {
        CameraControl cameraCtrl = Singleton<Studio>.Instance.cameraCtrl;
        cameraCtrl.Reset(0);
    }
    
    void ToggleClothesState()
    {
        OCIChar selectedChar = GetSelectedCharacter();
        if (selectedChar != null)
        {
            // 循环切换上衣状态
            byte currentState = selectedChar.charFileStatus.clothesState[0];
            byte newState = (byte)((currentState + 1) % 3);
            selectedChar.charInfo.SetClothesState(0, newState, true);
        }
    }
    
    void ToggleIKMode()
    {
        OCIChar selectedChar = GetSelectedCharacter();
        if (selectedChar != null)
        {
            bool isIKEnabled = selectedChar.oiCharInfo.enableIK;
            selectedChar.ActiveKinematicMode(
                isIKEnabled ? OICharInfo.KinematicMode.FK : OICharInfo.KinematicMode.IK, 
                true, 
                true
            );
        }
    }
}
```

### 拖拽交互

```csharp
public class DragInteraction : MonoBehaviour
{
    private bool isDragging = false;
    private ObjectCtrlInfo draggedObject = null;
    private Vector3 dragOffset;
    private Camera playerCamera;
    
    void Start()
    {
        playerCamera = Camera.main;
    }
    
    void Update()
    {
        if (Input.GetMouseButtonDown(0))
        {
            StartDrag();
        }
        
        if (Input.GetMouseButton(0) && isDragging)
        {
            ContinueDrag();
        }
        
        if (Input.GetMouseButtonUp(0))
        {
            EndDrag();
        }
    }
    
    void StartDrag()
    {
        Ray ray = playerCamera.ScreenPointToRay(Input.mousePosition);
        RaycastHit hit;
        
        if (Physics.Raycast(ray, out hit))
        {
            // 检查是否点击到可拖拽对象
            OCIChar character = hit.collider.GetComponentInParent<OCIChar>();
            if (character != null)
            {
                draggedObject = character;
                isDragging = true;
                dragOffset = hit.point - character.guideObject.transformTarget.position;
            }
            
            OCIItem item = hit.collider.GetComponentInParent<OCIItem>();
            if (item != null)
            {
                draggedObject = item;
                isDragging = true;
                dragOffset = hit.point - item.guideObject.transformTarget.position;
            }
        }
    }
    
    void ContinueDrag()
    {
        if (draggedObject == null) return;
        
        Ray ray = playerCamera.ScreenPointToRay(Input.mousePosition);
        Plane plane = new Plane(Vector3.up, draggedObject.guideObject.transformTarget.position);
        float distance;
        
        if (plane.Raycast(ray, out distance))
        {
            Vector3 newPosition = ray.GetPoint(distance) - dragOffset;
            draggedObject.guideObject.changeAmount.pos = newPosition;
        }
    }
    
    void EndDrag()
    {
        isDragging = false;
        draggedObject = null;
    }
}
```

---

## 实用示例

### 示例1：自动摆姿势系统

```csharp
public class AutoPoseSystem : MonoBehaviour
{
    [System.Serializable]
    public class PoseData
    {
        public string poseName;
        public Vector3[] bonePositions;
        public Vector3[] boneRotations;
        public int[] boneIndices;
    }
    
    public PoseData[] poses;
    private int currentPoseIndex = 0;
    
    public void ApplyPose(int poseIndex)
    {
        if (poseIndex < 0 || poseIndex >= poses.Length) return;
        
        OCIChar selectedChar = GetSelectedCharacter();
        if (selectedChar == null) return;
        
        PoseData pose = poses[poseIndex];
        
        // 启用FK模式
        selectedChar.ActiveKinematicMode(OICharInfo.KinematicMode.FK, true, true);
        
        // 应用骨骼位置和旋转
        for (int i = 0; i < pose.boneIndices.Length; i++)
        {
            int boneIndex = pose.boneIndices[i];
            if (boneIndex < selectedChar.listBones.Count)
            {
                OCIChar.BoneInfo boneInfo = selectedChar.listBones[boneIndex];
                boneInfo.guideObject.changeAmount.pos = pose.bonePositions[i];
                boneInfo.guideObject.changeAmount.rot = pose.boneRotations[i];
            }
        }
        
        currentPoseIndex = poseIndex;
    }
    
    public void NextPose()
    {
        ApplyPose((currentPoseIndex + 1) % poses.Length);
    }
    
    public void PreviousPose()
    {
        ApplyPose((currentPoseIndex - 1 + poses.Length) % poses.Length);
    }
}
```

### 示例2：相机路径系统

```csharp
public class CameraPathSystem : MonoBehaviour
{
    [System.Serializable]
    public class CameraKeyframe
    {
        public Vector3 position;
        public Vector3 rotation;
        public float fieldOfView;
        public float time;
    }
    
    public CameraKeyframe[] keyframes;
    public float pathDuration = 10.0f;
    public bool loop = true;
    
    private float currentTime = 0.0f;
    private CameraControl cameraCtrl;
    
    void Start()
    {
        cameraCtrl = Singleton<Studio>.Instance.cameraCtrl;
    }
    
    void Update()
    {
        if (keyframes.Length < 2) return;
        
        currentTime += Time.deltaTime;
        if (currentTime > pathDuration)
        {
            if (loop)
            {
                currentTime = 0.0f;
            }
            else
            {
                currentTime = pathDuration;
            }
        }
        
        float normalizedTime = currentTime / pathDuration;
        ApplyCameraKeyframe(normalizedTime);
    }
    
    void ApplyCameraKeyframe(float t)
    {
        int keyframeCount = keyframes.Length;
        float keyframeTime = t * (keyframeCount - 1);
        int keyframeIndex = Mathf.FloorToInt(keyframeTime);
        float keyframeBlend = keyframeTime - keyframeIndex;
        
        if (keyframeIndex >= keyframeCount - 1)
        {
            keyframeIndex = keyframeCount - 2;
            keyframeBlend = 1.0f;
        }
        
        CameraKeyframe kf1 = keyframes[keyframeIndex];
        CameraKeyframe kf2 = keyframes[keyframeIndex + 1];
        
        // 插值位置
        cameraCtrl.cameraData.pos = Vector3.Lerp(kf1.position, kf2.position, keyframeBlend);
        
        // 插值旋转
        cameraCtrl.cameraData.rotate = Vector3.Lerp(kf1.rotation, kf2.rotation, keyframeBlend);
        
        // 插值视野
        cameraCtrl.fieldOfView = Mathf.Lerp(kf1.fieldOfView, kf2.fieldOfView, keyframeBlend);
    }
}
```

### 示例3：表情动画系统

```csharp
public class ExpressionAnimationSystem : MonoBehaviour
{
    [System.Serializable]
    public class ExpressionKeyframe
    {
        public string expressionName;
        public float[] expressionValues;
        public float time;
    }
    
    public ExpressionKeyframe[] expressionKeyframes;
    public float animationDuration = 5.0f;
    public bool loop = true;
    
    private float currentTime = 0.0f;
    private OCIChar targetCharacter;
    
    void Start()
    {
        targetCharacter = GetSelectedCharacter();
    }
    
    void Update()
    {
        if (targetCharacter == null || expressionKeyframes.Length < 2) return;
        
        currentTime += Time.deltaTime;
        if (currentTime > animationDuration)
        {
            if (loop)
            {
                currentTime = 0.0f;
            }
            else
            {
                currentTime = animationDuration;
            }
        }
        
        float normalizedTime = currentTime / animationDuration;
        ApplyExpressionKeyframe(normalizedTime);
    }
    
    void ApplyExpressionKeyframe(float t)
    {
        int keyframeCount = expressionKeyframes.Length;
        float keyframeTime = t * (keyframeCount - 1);
        int keyframeIndex = Mathf.FloorToInt(keyframeTime);
        float keyframeBlend = keyframeTime - keyframeIndex;
        
        if (keyframeIndex >= keyframeCount - 1)
        {
            keyframeIndex = keyframeCount - 2;
            keyframeBlend = 1.0f;
        }
        
        ExpressionKeyframe kf1 = expressionKeyframes[keyframeIndex];
        ExpressionKeyframe kf2 = expressionKeyframes[keyframeIndex + 1];
        
        // 插值表情值
        for (int i = 0; i < kf1.expressionValues.Length; i++)
        {
            float value = Mathf.Lerp(kf1.expressionValues[i], kf2.expressionValues[i], keyframeBlend);
            targetCharacter.charInfo.fileStatus.expression[i] = value;
        }
    }
}
```

### 示例4：衣物动画系统

```csharp
public class ClothesAnimationSystem : MonoBehaviour
{
    [System.Serializable]
    public class ClothesKeyframe
    {
        public byte[] clothesStates;
        public bool[] accessoryStates;
        public float time;
    }
    
    public ClothesKeyframe[] clothesKeyframes;
    public float animationDuration = 3.0f;
    public bool loop = true;
    
    private float currentTime = 0.0f;
    private OCIChar targetCharacter;
    
    void Start()
    {
        targetCharacter = GetSelectedCharacter();
    }
    
    void Update()
    {
        if (targetCharacter == null || clothesKeyframes.Length < 2) return;
        
        currentTime += Time.deltaTime;
        if (currentTime > animationDuration)
        {
            if (loop)
            {
                currentTime = 0.0f;
            }
            else
            {
                currentTime = animationDuration;
            }
        }
        
        float normalizedTime = currentTime / animationDuration;
        ApplyClothesKeyframe(normalizedTime);
    }
    
    void ApplyClothesKeyframe(float t)
    {
        int keyframeCount = clothesKeyframes.Length;
        float keyframeTime = t * (keyframeCount - 1);
        int keyframeIndex = Mathf.FloorToInt(keyframeTime);
        float keyframeBlend = keyframeTime - keyframeIndex;
        
        if (keyframeIndex >= keyframeCount - 1)
        {
            keyframeIndex = keyframeCount - 2;
            keyframeBlend = 1.0f;
        }
        
        ClothesKeyframe kf1 = clothesKeyframes[keyframeIndex];
        ClothesKeyframe kf2 = clothesKeyframes[keyframeIndex + 1];
        
        // 应用衣物状态
        for (int i = 0; i < kf1.clothesStates.Length; i++)
        {
            byte state = (byte)Mathf.RoundToInt(
                Mathf.Lerp(kf1.clothesStates[i], kf2.clothesStates[i], keyframeBlend)
            );
            targetCharacter.charInfo.SetClothesState(i, state, false);
        }
        
        // 应用配饰状态
        for (int i = 0; i < kf1.accessoryStates.Length; i++)
        {
            bool state = keyframeBlend < 0.5f ? kf1.accessoryStates[i] : kf2.accessoryStates[i];
            targetCharacter.SetAccessoryState(i, state);
        }
    }
}
```

---

## 总结

这个教程涵盖了HS2 Studio开发的主要方面：

1. **基础概念** - 了解核心类结构和获取实例的方法
2. **人物控制** - 位置、旋转、状态、表情等控制
3. **相机系统** - 相机位置、旋转、目标设置等
4. **IK/FK系统** - 骨骼控制、IK目标设置等
5. **动画系统** - 动画播放、参数设置等
6. **衣物系统** - 衣物状态、配饰控制等
7. **物品系统** - 物品添加、控制、动画等
8. **第一人称视角** - 创建第一人称相机和移动控制
9. **交互系统** - 鼠标、键盘、拖拽交互
10. **实用示例** - 自动摆姿势、相机路径、表情动画、衣物动画等

通过这些API和示例，你可以开发出功能丰富的HS2 Studio插件。记住要经常参考反编译的代码来了解更多的API细节和用法。

---

## 注意事项

1. **性能优化** - 在处理大量对象时要注意性能优化
2. **错误处理** - 始终检查对象是否为null再进行操作
3. **内存管理** - 及时释放不需要的资源
4. **兼容性** - 注意不同版本HS2的API变化
5. **调试** - 使用Unity的调试工具来排查问题

希望这个教程能帮助你开发出优秀的HS2 Studio插件！ 