# HS2 Studio插件开发教程 (完整版)

## 前言

本教程旨在根据`Assembly-CSharp`反编译后的代码，为HS2 Studio插件开发者提供一份关于核心API和接口的参考。由于是基于反编译代码的推测，部分内容可能不完全准确，需要开发者在实践中进一步验证。

---

## 1. 角色(Character)控制

在Studio中，对角色的操作是最核心的功能。这包括位置、旋转、状态、表情等。

### 1.1. 关键类

- `Studio.OCIChar`: 代表场景中的一个角色，是所有角色操作的入口。
- `Studio.GuideObject`: 控制角色位置、旋转、缩放的变换控制器。
- `ChaControl`: 角色的主逻辑类，管理动画、服装、状态等。

### 1.2. 常用操作

- **获取当前选中的角色**:
  
  ```csharp
  // 获取当前选中的节点信息
  TreeNodeObject selectNode = Studio.Studio.Instance.treeNodeCtrl.selectNode;
  if (selectNode == null) return; // 没有选中任何东西
  
  // 尝试获取OCIChar
  OCIChar ociChar = selectNode.guideObject.oci as OCIChar;
  if (ociChar == null) return; // 选中的不是一个角色
  ```

- **修改角色位置/旋转/缩放**:
  
  ```csharp
  Transform charTransform = ociChar.guideObject.transform;
  
  // 直接设置新的世界坐标
  charTransform.position = new Vector3(1f, 0f, 2f);
  // 在现有旋转基础上进行旋转
  charTransform.Rotate(0f, 15f, 0f);
  // 修改缩放 (慎用)
  charTransform.localScale = new Vector3(1f, 1.1f, 1f);
  ```

---

## 2. 动画(Animation)控制

### 2.1. 关键类

- `AnimationAssist`: 一个非常方便的动画控制封装类。
- `Animation`: Unity的原生动画组件，在`ChaControl.animBody`中可以获取。

### 2.2. 常用操作

- **获取AnimationAssist实例**:
  
  ```csharp
  ChaControl chaCtrl = ociChar.charInfo;
  Animation anim = chaCtrl.animBody;
  AnimationAssist animAssist = new AnimationAssist(anim);
  ```

- **播放/停止/变速**:
  
  ```csharp
  // 通过索引播放，循环模式
  animAssist.Play(5, -1f, 0.3f, 0, WrapMode.Loop);
  // 停止
  animAssist.Stop();
  // 2倍速播放
  animAssist.SpeedSet(2.0f);
  ```

- **获取动画列表**:
  
  ```csharp
  for (int i = 0; i < animAssist.Data.info.Length; i++)
  {
      string animName = animAssist.GetID(i);
      Debug.Log($"动画索引: {i}, 名称: {animName}");
  }
  ```

---

## 3. 衣物(Cloth)与配件

### 3.1. 关键方法

- `ChaControl.SetClothesState`: 设置衣物的显示状态（穿着、半脱、全脱）。

### 3.2. 常用操作

- **显示/隐藏特定衣物**:
  
  ```csharp
  // 槽位定义 (通常在'ChaFileDefine.ClothesKind'枚举中)
  const int clothes_top = 0;    // 上衣
  const int clothes_shoes = 7;  // 鞋子
  
  // 隐藏上衣 (状态2为全脱)
  ociChar.charInfo.SetClothesState(clothes_top, 2, true);
  // 穿上鞋子 (状态0为穿着)
  ociChar.charInfo.SetClothesState(clothes_shoes, 0, true);
  ```

---

## 4. 场景(Scene)与相机(Camera)

### 4.1. 关键类/属性

- `Camera.main`: 获取Unity主相机。
- `Studio.Studio.Instance.cameraCtrl`: 获取Studio默认的相机控制器。

### 4.2. 常用操作

- **实现第一人称视角 (FPV)及移动**:
  你需要禁用默认相机控制，并将相机Transform同步到角色头部骨骼。
  
  ```csharp
  public class FirstPersonController : MonoBehaviour
  {
      private OCIChar targetChar;
      private Transform headBone;
      private Transform charTransform;
      private Camera mainCamera;
      public float moveSpeed = 2.0f;
  
      public void SetTarget(OCIChar ociChar)
      {
          targetChar = ociChar;
          charTransform = ociChar.guideObject.transform;
          headBone = ociChar.charInfo.animBody.transform.Find("cf_J_Head");
          mainCamera = Camera.main;
          Studio.Studio.Instance.cameraCtrl.enabled = false;
      }
  
      void LateUpdate()
      {
          if (targetChar == null) return;
          mainCamera.transform.position = headBone.position;
          mainCamera.transform.rotation = headBone.rotation;
  
          float moveHorizontal = Input.GetAxis("Horizontal");
          float moveVertical = Input.GetAxis("Vertical");
          Vector3 moveDirection = charTransform.forward * moveVertical + charTransform.right * moveHorizontal;
          charTransform.position += moveDirection * moveSpeed * Time.deltaTime;
      }
  
      void OnDestroy()
      {
          if (Studio.Studio.Instance != null) Studio.Studio.Instance.cameraCtrl.enabled = true;
      }
  }
  ```

---

## 6. 交互系统开发 (因为第5章是UI，我们先跳到交互)

### 6.1. 关键概念

- **Collider (碰撞体)**: 定义物体的物理形状，用于检测。
- **Raycast (射线投射)**: 模拟鼠标点击或视线检测。
- **Trigger (触发器)**: 特殊的碰撞体，用于检测进入/离开事件。

### 6.2. 常用操作

- **实现鼠标点击拾取物品**:
  
  ```csharp
  // 在Update中检测鼠标点击
  if (Input.GetMouseButtonDown(0))
  {
      Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
      if (Physics.Raycast(ray, out RaycastHit hitInfo, 100f))
      {
          Debug.Log("击中了: " + hitInfo.collider.name);
          // 可以在此添加拾取逻辑
      }
  }
  ```

- **实现角色走进区域触发事件**:
  
  ```csharp
  // 挂载到触发器区域的脚本
  public class EventTriggerZone : MonoBehaviour
  {
      void OnTriggerEnter(Collider other)
      {
          Debug.Log(other.name + " 进入了区域");
          // 需要给角色添加Rigidbody才能触发
      }
  }
  ```

---

## 7. 骨骼与IK (反向动力学)

### 7.1. 关键属性

- `OCIChar.ikInfo`: 存储角色IK信息的数组。
- `OCIChar.IKInfo.active`: 布尔值，开关特定部位的IK。
- `OCIChar.IKInfo.oiikTargetInfo`: IK目标控制器。

### 7.2. 常用操作

- **启用/禁用特定部位的IK**:
  
  ```csharp
  const int IK_LEFT_ARM = 1;
  const int IK_RIGHT_LEG = 4;
  
  // 启用左手的IK
  ociChar.ikInfo[IK_LEFT_ARM].active = true;
  // 禁用右腿的IK
  ociChar.ikInfo[IK_RIGHT_LEG].active = false;
  ```

- **修改IK目标的位置**:
  
  ```csharp
  // 获取右手IK的目标控制器
  GuideObject ikTargetGuide = ociChar.ikInfo[IK_RIGHT_ARM].oiikTargetInfo.guideObject;
  // 将IK目标移动到世界原点
  ikTargetGuide.transform.position = Vector3.zero;
  ```